import { z } from "zod";

// Define gear types
export enum GearType {
  SHIRT = "shirt",
  PANTS = "pants",
  JACKET = "jacket",
  BOOTS = "boots",
  HELMET = "helmet",
  GLOVES = "gloves",
  SAFETY_VEST = "safety_vest",
  OTHER = "other",
}

// Define sizes
export enum Size {
  XS = "xs",
  S = "s",
  M = "m",
  L = "l",
  XL = "xl",
  XXL = "xxl",
  XXXL = "xxxl",
}

// Schema for uniform request form
export const uniformRequestSchema = z.object({
  gearType: z.nativeEnum(GearType, {
    errorMap: () => ({ message: "Please select a gear type" }),
  }),
  size: z.nativeEnum(Size, {
    errorMap: () => ({ message: "Please select a size" }),
  }),
  reason: z.string().min(5, { message: "Reason must be at least 5 characters" }),
});

// Type for uniform request form
export type UniformRequestFormData = z.infer<typeof uniformRequestSchema>;

// Default values for uniform request form
export const defaultUniformRequestValues: UniformRequestFormData = {
  gearType: GearType.SHIRT,
  size: Size.M,
  reason: "",
};

// Helper function to get gear type options for dropdown
export const getGearTypeOptions = () => {
  return Object.entries(GearType).map(([key, value]) => ({
    label: key.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
    value,
  }));
};

// Helper function to get size options for dropdown
export const getSizeOptions = () => {
  return Object.entries(Size).map(([key, value]) => ({
    label: key,
    value,
  }));
};
