import { z } from "zod";

// Define priority levels
export enum PriorityLevel {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

// Define incident types (for reference, not used in the form directly)
export enum IncidentType {
  SAFETY = "safety",
  SECURITY = "security",
  MAINTENANCE = "maintenance",
  ENVIRONMENTAL = "environmental",
  OTHER = "other",
}

// Schema for incident reporting form
export const incidentReportSchema = z.object({
  date: z.string().min(1, { message: "Date is required" }),
  location: z.string().min(1, { message: "Location is required" }),
  description: z
    .string()
    .min(10, { message: "Description must be at least 10 characters" })
    .max(500, { message: "Description must be less than 500 characters" }),
  type: z.string().min(1, { message: "Type is required" }),
  priorityLevel: z.nativeEnum(PriorityLevel, {
    errorMap: () => ({ message: "Please select a priority level" }),
  }),
  evidence: z.string().nullable(),
});

// Type for incident report form
export type IncidentReportFormData = z.infer<typeof incidentReportSchema>;

// Default values for incident report form
export const defaultIncidentReportValues: IncidentReportFormData = {
  date: "",
  location: "",
  description: "",
  type: "",
  priorityLevel: PriorityLevel.MEDIUM,
  evidence: null,
};

// Helper function to get priority level options for dropdown
export const getPriorityLevelOptions = () => {
  return Object.entries(PriorityLevel).map(([key, value]) => ({
    label: key.toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase()),
    value,
  }));
};
