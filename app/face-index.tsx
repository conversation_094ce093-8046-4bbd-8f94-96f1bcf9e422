import { Form } from "@/components/forms";
import { FormCameraInput } from "@/components/forms/FormCameraInput";
import { Button } from "@/components/ui/Button";
import { Colors } from "@/constants/Colors";
import {
  useClearFaceMutation,
  useIndexFaceMutation,
} from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";
import { zodResolver } from "@hookform/resolvers/zod";
import React from "react";
import { useForm } from "react-hook-form";
import { StyleSheet, Text, View } from "react-native";
import { z } from "zod";

const faceIndexSchema = z.object({
  userId: z.string(),
  base64Img: z.string().optional(),
});

type FaceIndexFormValues = z.infer<typeof faceIndexSchema>;

export default function FaceIndexScreen() {
  const { session } = useSession();

  const userId = session?.userId || "";
  const { mutateAsync: indexFace } = useIndexFaceMutation();
  const { mutateAsync: clearFaceIndex, isLoading: clearingFaceIndex } =
    useClearFaceMutation();

  const methods = useForm<FaceIndexFormValues>({
    resolver: zodResolver(faceIndexSchema),
    defaultValues: {
      userId,
    },
  });
  const base64Img = methods.watch("base64Img");

  const handleFaceIndex = methods.handleSubmit(
    async (data: FaceIndexFormValues) => {
      try {
        if (!data.base64Img) {
          console.warn("No base64 image provided");
          return;
        }

        const base64Data = data.base64Img.split(",")[1]; // Remove data:image prefix
        await indexFace({
          indexFaceInput: {
            base64Img: base64Data,
            userId: data.userId,
          },
        });

        console.log("Face indexed successfully");
        methods.setValue("base64Img", undefined);
      } catch (error) {
        console.error("Error indexing face:", error);
      }
    }
  );

  const handleClearFaceIndex = async () => {
    try {
      await clearFaceIndex({
        input: {
          userId,
        },
      });
      console.log("Face index cleared successfully");
      // Clear the image field
      methods.setValue("base64Img", undefined);
    } catch (error) {
      console.error("Error clearing face index:", error);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Register Face Index</Text>
      <Form>
        <FormCameraInput
          name="base64Img"
          control={methods.control}
          label="Take a Face Photo"
        />
        <View style={styles.btnContainer}>
          <Button
            style={{ flex: 1 }}
            title="Index Face"
            onPress={handleFaceIndex}
          />
          <Button
            style={{
              flex: 1,
              backgroundColor: base64Img ? Colors.warning : "#ccc", // greyed out if no image
            }}
            textStyle={{ color: base64Img ? Colors.text : "#999" }}
            title="Clear Face Index"
            onPress={handleClearFaceIndex}
            disabled={!base64Img}
          />
        </View>
      </Form>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 8,
  },
  btnContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 8,
  },
  heading: {
    fontSize: 18,
    fontWeight: "500",
    marginBottom: 16,
    color: "#333",
    textDecorationLine: "underline",
    textDecorationStyle: "solid",
    textDecorationColor: "#333",
    paddingLeft: 16,
  },
});
