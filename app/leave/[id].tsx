import React, { useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { StatusBar } from "@/components/ui/StatusBar";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withDelay,
  Easing,
  FadeIn,
} from "react-native-reanimated";
import { Colors } from "@/constants/Colors";
// Define leave types directly in this file for now
enum LeaveType {
  FULLDAY = "fullday",
  HALFDAY = "halfday",
}

enum LeaveStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

// Define types for leave details
type BaseLeave = {
  id: string;
  reason: string;
  leaveType: LeaveType;
  startDateTime: Date;
  endDateTime: Date;
  leaveStatus: LeaveStatus;
};

type ApprovedLeave = BaseLeave & {
  leaveStatus: LeaveStatus.APPROVED;
  approvedBy: string;
  approvedOn: Date;
};

type RejectedLeave = BaseLeave & {
  leaveStatus: LeaveStatus.REJECTED;
  rejectedReason: string;
  rejectedBy: string;
  rejectedOn: Date;
};

type PendingLeave = BaseLeave & {
  leaveStatus: LeaveStatus.PENDING;
  requestedOn: Date;
};

type Leave = ApprovedLeave | RejectedLeave | PendingLeave;

/**
 * Detail Item Component with Animation
 */
function DetailItem({
  label,
  value,
  icon,
  index = 0,
}: {
  label: string;
  value: string;
  icon: keyof typeof Ionicons.glyphMap;
  index?: number;
}) {
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(20);

  useEffect(() => {
    opacity.value = withDelay(
      100 * index,
      withTiming(1, {
        duration: 500,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      })
    );
    translateY.value = withDelay(
      100 * index,
      withSpring(0, { damping: 12, stiffness: 100 })
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [{ translateY: translateY.value }],
    };
  });

  return (
    <Animated.View style={[styles.detailItem, animatedStyle]}>
      <View style={styles.detailIconContainer}>
        <Ionicons name={icon} size={20} color={Colors.white} />
      </View>
      <View style={styles.detailContent}>
        <Text style={styles.detailLabel}>{label}</Text>
        <Text style={styles.detailValue}>{value}</Text>
      </View>
    </Animated.View>
  );
}

/**
 * Status Badge Component with Animation
 */
function StatusBadge({ status }: { status: LeaveStatus }) {
  const scale = useSharedValue(0.8);
  const opacity = useSharedValue(0);

  // Get status color
  const getStatusColor = (status: LeaveStatus): string => {
    switch (status) {
      case LeaveStatus.APPROVED:
        return Colors.success;
      case LeaveStatus.REJECTED:
        return Colors.error;
      case LeaveStatus.PENDING:
      default:
        return Colors.warning;
    }
  };

  useEffect(() => {
    scale.value = withSpring(1, { damping: 12, stiffness: 120 });
    opacity.value = withTiming(1, { duration: 400 });
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [{ scale: scale.value }],
    };
  });

  return (
    <Animated.View
      style={[
        styles.statusBadge,
        { backgroundColor: getStatusColor(status) },
        animatedStyle,
      ]}
    >
      <Text style={styles.statusText}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Text>
    </Animated.View>
  );
}

/**
 * Leave Detail Screen
 */
export default function LeaveDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const leaveId = typeof id === "string" ? id : "1";
  // Use type assertion to fix TypeScript error
  const leave = leaveDetails[leaveId as keyof typeof leaveDetails];

  // Animation values
  const headerOpacity = useSharedValue(0);
  const cardScale = useSharedValue(0.95);
  const cardOpacity = useSharedValue(0);

  useEffect(() => {
    // Animate header
    headerOpacity.value = withTiming(1, { duration: 500 });

    // Animate card
    cardScale.value = withTiming(1, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
    cardOpacity.value = withTiming(1, { duration: 600 });
  }, []);

  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: headerOpacity.value,
    };
  });

  const cardAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: cardOpacity.value,
      transform: [{ scale: cardScale.value }],
    };
  });

  // Format date
  const formatDate = (date: Date | string): string => {
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    return new Date(date).toLocaleDateString("en-US", options);
  };

  // Format date range
  const formatDateRange = (
    start: Date | string,
    end: Date | string
  ): string => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    const startFormatted = startDate.toLocaleDateString("en-US", options);

    if (startDate.getTime() === endDate.getTime()) {
      return startFormatted;
    }

    const endFormatted = endDate.toLocaleDateString("en-US", options);
    return `${startFormatted} to ${endFormatted}`;
  };

  // Get leave type label
  const getLeaveTypeLabel = (type: LeaveType): string => {
    return type === LeaveType.FULLDAY ? "Full Day" : "Half Day";
  };

  // Calculate duration in days
  const calculateDuration = (
    start: Date | string,
    end: Date | string
  ): string => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    // Calculate difference in days
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays + (diffDays === 1 ? " day" : " days");
  };

  // Handle back button press
  const handleBackPress = () => {
    router.back();
  };

  // Handle cancel leave request
  const handleCancelRequest = () => {
    // In a real app, this would call an API to cancel the request

    // Navigate back immediately
    router.back();
  };

  if (!leave) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
            <Ionicons name="chevron-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Leave Details</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Leave request not found</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light" />

      {/* Header */}
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Leave Details</Text>
      </Animated.View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View style={[styles.card, cardAnimatedStyle]}>
          {/* Leave Header */}
          <View style={styles.leaveHeader}>
            <Text style={styles.leaveReason}>{leave.reason}</Text>
            <StatusBadge status={leave.leaveStatus} />
          </View>

          {/* Leave Details */}
          <View style={styles.detailsContainer}>
            <DetailItem
              label="Date"
              value={formatDateRange(leave.startDateTime, leave.endDateTime)}
              icon="calendar-outline"
              index={0}
            />

            <DetailItem
              label="Duration"
              value={calculateDuration(leave.startDateTime, leave.endDateTime)}
              icon="time-outline"
              index={1}
            />

            <DetailItem
              label="Leave Type"
              value={getLeaveTypeLabel(leave.leaveType)}
              icon="layers-outline"
              index={2}
            />

            <DetailItem
              label="Requested On"
              value={formatDate(
                leave.leaveStatus === LeaveStatus.PENDING
                  ? (leave as PendingLeave).requestedOn
                  : leave.startDateTime
              )}
              icon="create-outline"
              index={3}
            />

            {leave.leaveStatus === LeaveStatus.APPROVED && (
              <>
                <DetailItem
                  label="Approved By"
                  value={leave.approvedBy}
                  icon="checkmark-circle-outline"
                  index={4}
                />

                <DetailItem
                  label="Approved On"
                  value={formatDate(leave.approvedOn)}
                  icon="checkmark-done-outline"
                  index={5}
                />
              </>
            )}

            {leave.leaveStatus === LeaveStatus.REJECTED && (
              <>
                <DetailItem
                  label="Rejected By"
                  value={leave.rejectedBy}
                  icon="close-circle-outline"
                  index={4}
                />

                <DetailItem
                  label="Rejected On"
                  value={formatDate(leave.rejectedOn)}
                  icon="close-outline"
                  index={5}
                />

                <Animated.View
                  style={styles.rejectionContainer}
                  entering={FadeIn.delay(600).duration(500)}
                >
                  <Text style={styles.rejectionLabel}>Rejection Reason:</Text>
                  <Text style={styles.rejectionReason}>
                    {leave.rejectedReason}
                  </Text>
                </Animated.View>
              </>
            )}
          </View>

          {/* Cancel Button (only for pending requests) */}
          {leave.leaveStatus === LeaveStatus.PENDING && (
            <Animated.View
              entering={FadeIn.delay(700).duration(400)}
              style={styles.cancelButtonContainer}
            >
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={handleCancelRequest}
              >
                <Ionicons
                  name="close-circle-outline"
                  size={20}
                  color={Colors.white}
                />
                <Text style={styles.cancelButtonText}>Cancel Request</Text>
              </TouchableOpacity>
            </Animated.View>
          )}
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
    elevation: 2,
  },
  leaveHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  leaveReason: {
    fontSize: 20,
    fontWeight: "600",
    color: Colors.text,
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.white,
  },
  detailsContainer: {
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: "row",
    marginBottom: 16,
  },
  detailIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
    borderWidth: 1,
    borderColor: "rgba(255,255,255,0.2)",
  },
  detailContent: {
    flex: 1,
    justifyContent: "center",
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: Colors.text,
    fontWeight: "500",
  },
  rejectionContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: "rgba(255,0,0,0.05)",
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.error,
  },
  rejectionLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.error,
    marginBottom: 8,
  },
  rejectionReason: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 22,
  },
  cancelButtonContainer: {
    marginTop: 8,
  },
  cancelButton: {
    backgroundColor: Colors.error,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  cancelButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
});
