import {
  Form,
  FormDatePicker,
  FormInput,
  FormRadioGroup,
  FormSubmitButton,
} from "@/components/forms";
import { StatusBar } from "@/components/ui/StatusBar";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "expo-router";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

import {
  LeaveType,
  useCreateLeaveMutation,
  useGetLeavesQuery,
} from "@/generated/graphql";
import { enumToOptions, errorToast, successToast } from "@/lib/utils";
import { useSession } from "@/providers/auth-provider";
import { z } from "zod";
import { queryClient } from "../_layout";

const leaveRequestSchema = z
  .object({
    reason: z
      .string()
      .min(3, { message: "Reason must be at least 3 characters" })
      .max(200, { message: "Reason must be less than 200 characters" }),
    leaveType: z.nativeEnum(LeaveType),
    startDate: z.string().min(1, { message: "Start date is required" }),
    endDate: z.string().nullable(),
  })
  .refine(
    (data) => {
      // If it's a full day leave, end date is required
      if (data.leaveType === LeaveType.Fullday && !data.endDate) {
        return false;
      }
      // For half day leave, if startDate is set, automatically set endDate to the same value
      if (
        data.leaveType === LeaveType.Halfday &&
        data.startDate &&
        !data.endDate
      ) {
        data.endDate = data.startDate;
      }
      return true;
    },
    {
      message: "End date is required for full day leave",
      path: ["endDate"],
    }
  );

type LeaveRequestFormData = z.infer<typeof leaveRequestSchema>;

export const defaultLeaveRequestValues: LeaveRequestFormData = {
  reason: "",
  leaveType: LeaveType.Fullday,
  startDate: "",
  endDate: null,
};

export default function RequestLeaveScreen() {
  const router = useRouter();
  const { session } = useSession();

  // Initialize the create leave mutation
  const { mutateAsync: createLeave } = useCreateLeaveMutation();

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    watch,
    reset,
    formState: { isValid },
  } = useForm<LeaveRequestFormData>({
    defaultValues: defaultLeaveRequestValues,
    resolver: zodResolver(leaveRequestSchema),
    mode: "onChange",
  });

  // Watch form values for conditional rendering
  const leaveType = watch("leaveType");

  // Handle form submission
  const onSubmit = async (data: LeaveRequestFormData) => {
    try {
      // Convert form data to the format expected by the GraphQL mutation
      const startDateTime = new Date(data.startDate);
      // For half-day leave, end date is the same as start date
      const endDateTime = data.endDate
        ? new Date(data.endDate)
        : new Date(data.startDate);

      // Set end time to end of day for full day leave
      if (data.leaveType === LeaveType.Fullday && endDateTime) {
        endDateTime.setHours(23, 59, 59, 999);
      }

      // Check if we have the user ID from the me query
      if (!session?.userId) {
        throw new Error("User ID not available. Please try again later.");
      }

      // Map AppLeaveType to GraphQLLeaveType
      const graphqlLeaveType =
        data.leaveType === LeaveType.Fullday
          ? LeaveType.Fullday
          : LeaveType.Halfday;

      // Submit the leave request
      await createLeave({
        input: {
          // Use the actual user ID from the me query
          user: session.userId,
          reason: data.reason,
          leaveType: graphqlLeaveType,
          startDateTime: startDateTime,
          endDateTime: endDateTime,
        },
      });
      successToast("Leave request submitted successfully!");
      reset(defaultLeaveRequestValues); // Reset form to default values

      // Success is handled in the onSuccess callback
    } catch (error) {
      // Error is handled in the onError callback
      errorToast(error);
    }
  };

  // Handle back button press
  const handleBackPress = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Request Leave</Text>
      </View>

      <Form>
        {/* Reason Input */}
        <FormInput
          name="reason"
          control={control}
          label="Reason for Leave"
          placeholder="Enter reason for leave"
          multiline
          numberOfLines={3}
        />

        {/* Leave Type Selection */}
        <FormRadioGroup
          name="leaveType"
          control={control}
          label="Leave Type"
          options={enumToOptions(LeaveType)}
        />

        {/* Date Selection */}
        <FormDatePicker
          name="startDate"
          control={control}
          label={LeaveType.Fullday ? "Date Range" : "Date"}
          placeholder={LeaveType.Fullday ? "Select date range" : "Select date"}
          isRange={leaveType === LeaveType.Fullday}
          endDateName="endDate"
          minDate={new Date().toISOString().split("T")[0]}
        />

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Submit Request"
          cancelLabel="Cancel"
          onSubmit={handleSubmit(onSubmit)}
          onCancel={handleBackPress}
          isValid={isValid}
          submitIcon="paper-plane-outline"
          cancelIcon="close-outline"
        />
      </Form>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "semibold",
    color: Colors.white,
  },
});
