import React from "react";
import { StyleSheet, View, Text, FlatList } from "react-native";
import { Colors } from "@/constants/Colors";
import { AnnouncementItem } from "@/components/AnnouncementItem";
import { useRouter } from "expo-router";
import { AnnouncementsQuery, useAnnouncementsQuery } from "@/generated/graphql";

export default function InboxScreen() {
  const router = useRouter();
  const { data: announcementsData, isLoading } = useAnnouncementsQuery();

  const handleAnnouncementPress = (
    announcement: AnnouncementsQuery["anouncements"][number]
  ) => {
    router.push(`/inbox/${announcement.id}` as any);
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={announcementsData?.anouncements}
        keyExtractor={(item) => item.id.toString()}
        refreshing={isLoading}
        renderItem={({ item }) => (
          <AnnouncementItem
            announcement={item}
            onPress={handleAnnouncementPress}
          />
        )}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  listContent: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    overflow: "hidden",
  },
});
