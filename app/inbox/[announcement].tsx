import { View, Text, StyleSheet, ActivityIndicator } from "react-native";
import React from "react";
import { useLocalSearchParams } from "expo-router";
import { useAnnouncementDetailQuery } from "@/generated/graphql";
import LoadingScreen from "@/components/LoadingView";

export default function InboxDetailScreen() {
  const { announcement } = useLocalSearchParams();
  const { data, isLoading } = useAnnouncementDetailQuery({
    id: announcement as string,
  });

  return (
    <View style={styles.container}>
      {isLoading ? (
        <LoadingScreen />
      ) : (
        <View>
          <Text style={{ fontSize: 24, fontWeight: "bold" }}>
            {data?.anouncement?.title}
          </Text>
          <Text style={{ marginTop: 10 }}>
            {data?.anouncement?.description}
          </Text>
          <Text style={{ marginTop: 10, color: "gray" }}>
            Posted on:{" "}
            {data?.anouncement?.createdAt
              ? new Date(data.anouncement.createdAt).toLocaleDateString()
              : "Unknown"}
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    justifyContent: "center",
    alignItems: "center",
  },
});
