import React, { useState } from "react";
import { StyleSheet, View, Alert } from "react-native";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Colors } from "@/constants/Colors";
import {
  Form,
  FormInput,
  FormSelect,
  FormSubmitButton,
} from "@/components/forms";
import {
  uniformRequestSchema,
  UniformRequestFormData,
  defaultUniformRequestValues,
  getGearTypeOptions,
  getSizeOptions,
} from "@/schemas/uniform-request";

export default function UniformRequestScreen() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<UniformRequestFormData>({
    defaultValues: defaultUniformRequestValues,
    resolver: zodResolver(uniformRequestSchema),
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = (data: UniformRequestFormData) => {
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      Alert.alert("Success", "Uniform request submitted successfully!", [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ]);
    }, 1500);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />

      <Form contentContainerStyle={styles.formContent}>
        {/* Gear Type Dropdown */}
        <FormSelect
          name="gearType"
          control={control}
          label="Select Gear"
          placeholder="Select Value"
          options={getGearTypeOptions()}
        />

        {/* Size Dropdown */}
        <FormSelect
          name="size"
          control={control}
          label="Select Size"
          placeholder="Select Value"
          options={getSizeOptions()}
        />

        {/* Reason Input */}
        <FormInput
          name="reason"
          control={control}
          label="Reason"
          placeholder="Enter Reason"
          multiline
          numberOfLines={4}
        />

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Request Gear"
          onSubmit={handleSubmit(onSubmit)}
          isSubmitting={isSubmitting}
          isValid={isValid}
          style={styles.submitButton}
          submitButtonStyle={{ backgroundColor: "#7AC142" }} // Green color from the image
        />
      </Form>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  formContent: {
    padding: 16,
    paddingBottom: 80, // Extra bottom padding
    paddingTop: 20,
  },
  submitButton: {
    marginTop: 20,
  },
});
