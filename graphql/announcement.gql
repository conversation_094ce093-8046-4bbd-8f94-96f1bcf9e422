query Announcements {
  anouncements {
    id
    title
    description
    date
    userRoles
    document
    users {
      id
      fullname
      profilePicture
    }
  }
}

query AnnouncementDetail($id: String!) {
  anouncement(id: $id) {
    title
    description
    document
    createdAt
    _id
    users {
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
    }
  }
}
