import { View, Text, ScrollView, StyleSheet } from "react-native";
import React from "react";
import { z } from "zod";
import { useForm, FormProvider, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useUpdateUserProfileMutation } from "@/generated/graphql";
import {
  Form,
  FormDatePicker,
  FormInput,
  FormSelect,
  FormSubmitButton,
} from "@/components/forms";
import { enumToOptions, errorToast, successToast } from "@/lib/utils";
import { Button } from "@/components/ui/Button";
import { Colors } from "@/constants/Colors";

enum Gender {
  male = "Male",
  female = "Female",
  other = "Other",
}
enum MaritalStatus {
  single = "Single",
  married = "Married",
  divorced = "Divorced",
  widowed = "Widowed",
}
enum CountryCode {
  MY = "+60",
  IN = "+91",
  US = "+1",
  UK = "+44",
  SG = "+65",
}

interface UserProfileFormProps {
  userId: string;
  initialData?: {
    ic?: string;
    ID?: string;
    passport?: string;
    passportExpiresAt?: Date;
    permitNumber?: string;
    permitExpiresAt?: Date;
    gender?: "male" | "female" | "other";
    dob?: Date;
    placeOfBirth?: string;
    currentAddress?: string;
    joinedAt?: Date;
    maritalStatus?: "single" | "married" | "divorced" | "widowed";
    bankAccNumber?: string;
    bankName?: string;
    emergencyContact?: Array<{
      name: string;
      relation: string;
      contact: {
        countryCode: string;
        phone: string;
      };
    }>;
  };
}

const contactSchema = z.object({
  countryCode: z.string().default("+60"),
  phone: z.string().min(1, "Phone number is required"),
});

const emergencyContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  relation: z.string().min(1, "Relation is required"),
  contact: contactSchema,
});

const userProfileSchema = z.object({
  ic: z.string().optional(),
  ID: z.string().optional(),
  passport: z.string().optional(),
  passportExpiresAt: z.date().optional(),
  permitNumber: z.string().optional(),
  permitExpiresAt: z.date().optional(),
  gender: z.enum(["male", "female", "other"]).optional(),
  dob: z.date().optional(),
  placeOfBirth: z.string().optional(),
  currentAddress: z.string().optional(),
  joinedAt: z.date().optional(),
  maritalStatus: z
    .enum(["single", "married", "divorced", "widowed"])
    .optional(),
  bankAccNumber: z.string().optional(),
  bankName: z.string().optional(),
  emergencyContact: z.array(emergencyContactSchema).optional(),
});

type UserProfileFormValues = z.infer<typeof userProfileSchema>;

export const useUpdateUserProfileFormMethods = (
  initialData?: UserProfileFormProps["initialData"]
) => {
  return useForm<UserProfileFormValues>({
    //@ts-ignore
    resolver: zodResolver(userProfileSchema),
    defaultValues: {
      ic: initialData?.ic ?? "",
      ID: initialData?.ID ?? "",
      passport: initialData?.passport ?? "",
      passportExpiresAt: initialData?.passportExpiresAt,
      permitNumber: initialData?.permitNumber ?? "",
      permitExpiresAt: initialData?.permitExpiresAt,
      gender: initialData?.gender,
      dob: initialData?.dob,
      placeOfBirth: initialData?.placeOfBirth ?? "",
      currentAddress: initialData?.currentAddress ?? "",
      joinedAt: initialData?.joinedAt,
      maritalStatus: initialData?.maritalStatus,
      bankAccNumber: initialData?.bankAccNumber ?? "",
      bankName: initialData?.bankName ?? "",
      emergencyContact: initialData?.emergencyContact ?? [],
    },
  });
};

export default function UserProfileForm({
  userId,
  initialData,
}: UserProfileFormProps) {
  const scrollRef = React.useRef<ScrollView>(null);

  const methods = useUpdateUserProfileFormMethods(initialData);
  const { mutateAsync: updateUserProfile } = useUpdateUserProfileMutation();

  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: "emergencyContact",
  });

  // Ensure at least one emergency contact exists
  React.useEffect(() => {
    if (fields.length === 0) {
      append({
        name: "",
        relation: "",
        contact: { countryCode: "+60", phone: "" },
      });
    }
  }, [fields, append]);

  const onSubmit = async (data: UserProfileFormValues) => {
    try {
      await updateUserProfile({
        userId,
        updateUserProfileInput: {
          ...data,
          passportExpiresAt: data?.passportExpiresAt ?? undefined,
          permitExpiresAt: data?.permitExpiresAt ?? undefined,
          dob: data?.dob ?? undefined,
          joinedAt: data?.joinedAt ?? undefined,
        },
      });
      successToast("Profile updated successfully!");
      methods.reset(data); // Reset form with updated data
    } catch (error) {
      errorToast(error);
    }
  };

  return (
    <FormProvider {...methods}>
      <ScrollView ref={scrollRef} contentContainerStyle={styles.container}>
        <FormInput
          control={methods.control}
          name="ic"
          label="IC Number"
          placeholder="Enter IC number"
        />
        <FormInput
          control={methods.control}
          name="ID"
          label="ID Number"
          placeholder="Enter ID number"
        />
        <FormInput
          control={methods.control}
          name="passport"
          label="Passport Number"
          placeholder="Enter passport number"
        />
        <FormDatePicker
          control={methods.control}
          name="passportExpiresAt"
          label="Passport Expiry Date"
          placeholder="Select passport expiry date"
          minDate={new Date().toDateString()}
        />
        <FormInput
          control={methods.control}
          name="permitNumber"
          label="Permit Number"
          placeholder="Enter permit number"
        />
        <FormDatePicker
          control={methods.control}
          name="permitExpiresAt"
          label="Permit Expiry Date"
          placeholder="Select permit expiry date"
        />
        <FormSelect
          control={methods.control}
          name="gender"
          options={enumToOptions(Gender)}
          label="Select Gender"
        />
        <FormDatePicker
          control={methods.control}
          name="dob"
          label="Date of birth"
          placeholder="Enter place of birth"
        />
        <FormInput
          control={methods.control}
          name="placeOfBirth"
          label="Place of Birth"
          placeholder="Enter place of birth"
        />
        <FormInput
          control={methods.control}
          name="currentAddress"
          label="Current Address"
          placeholder="Enter current address"
        />
        <FormDatePicker
          control={methods.control}
          name="joinedAt"
          label="Joined Date"
          placeholder="Select joined date"
        />
        <FormSelect
          control={methods.control}
          name="gender"
          options={enumToOptions(MaritalStatus)}
          label="Select marital status"
        />
        <FormInput
          control={methods.control}
          name="bankAccNumber"
          label="Bank Account Number"
          placeholder="Enter bank account number"
        />
        <FormInput
          control={methods.control}
          name="bankName"
          label="Bank Name"
          placeholder="Enter bank name"
        />
        <View>
          <Text
            style={{
              fontSize: 18,
              color: "#333",
              textDecorationStyle: "solid",
              textDecorationLine: "underline",
              marginVertical: 16,
              fontWeight: "500",
            }}
          >
            Emergency Contacts
          </Text>
          {fields.map((field, index) => (
            <View key={field.id} style={{ marginBottom: 16 }}>
              <FormInput
                control={methods.control}
                name={`emergencyContact.${index}.name`}
                label="Name"
                placeholder="Enter contact name"
              />
              <FormInput
                control={methods.control}
                name={`emergencyContact.${index}.relation`}
                label="Relation"
                placeholder="Enter relation"
              />
              <FormSelect
                control={methods.control}
                name={`emergencyContact.${index}.contact.countryCode`}
                label="Country Code"
                options={enumToOptions(CountryCode)}
                placeholder="Select country code"
              />
              <FormInput
                control={methods.control}
                name={`emergencyContact.${index}.contact.phone`}
                label="Phone Number"
                placeholder="Enter phone number"
              />
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: 8,
                }}
              >
                {index === fields.length - 1 && (
                  <Button
                    style={{
                      backgroundColor: Colors.primaryDark,
                      marginTop: 4,
                      width: 150,
                    }}
                    title="Add Contact"
                    textStyle={{ fontSize: 14 }}
                    onPress={() => {
                      append({
                        name: "",
                        relation: "",
                        contact: { countryCode: "+60", phone: "" },
                      });

                      // Wait for UI to update before scrolling
                      setTimeout(() => {
                        scrollRef.current?.scrollToEnd({ animated: true });
                      }, 100);
                    }}
                  />
                )}
                <Button
                  style={{
                    backgroundColor: Colors.error,
                    marginTop: 4,
                    width: 150,
                  }}
                  textStyle={{ fontSize: 14 }}
                  disabled={fields.length <= 1}
                  onPress={() => remove(index)}
                  title="Remove Contact"
                />
              </View>
            </View>
          ))}
        </View>
        <FormSubmitButton
          submitLabel="Submit"
          onSubmit={methods.handleSubmit(onSubmit)}
          isValid={methods.formState.isValid}
          style={styles.submitButton}
        />
      </ScrollView>
    </FormProvider>
  );
}
const styles = StyleSheet.create({
  container: { paddingBottom: 80, paddingHorizontal: 16 },
  submitButton: { marginBottom: 20 },
});
