import React from "react";
import { StyleSheet, View, Dimensions } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  withSpring,
  withSequence,
  Easing,
} from "react-native-reanimated";
import { IconButton } from "./ui/IconButton";
import { Card } from "./ui/Card";

// Get screen dimensions
const { width: SCREEN_WIDTH } = Dimensions.get("window");

interface MenuItem {
  icon: string;
  label: string;
  href: string;
}

interface MenuGridProps {
  items: MenuItem[];
}

/**
 * Grid of menu items with icons with eye-catching animations
 */
export function MenuGrid({ items }: MenuGridProps) {
  // Calculate icon size based on screen width
  // For 4 items per row with proper spacing
  const ITEMS_PER_ROW = 4;
  const HORIZONTAL_PADDING = 20; // Total horizontal padding
  const ITEM_SPACING = 16; // Increased space between items for better distribution
  const AVAILABLE_WIDTH = SCREEN_WIDTH - HORIZONTAL_PADDING * 2;
  const ITEM_WIDTH =
    (AVAILABLE_WIDTH - ITEM_SPACING * (ITEMS_PER_ROW - 1)) / ITEMS_PER_ROW;

  // Calculate icon size proportionally to item width
  const ICON_SIZE = Math.max(22, Math.min(28, ITEM_WIDTH * 0.5));

  // Group items into rows of 4 to ensure consistent layout
  const rows = [];
  for (let i = 0; i < items.length; i += ITEMS_PER_ROW) {
    rows.push(items.slice(i, i + ITEMS_PER_ROW));
  }

  return (
    <Card style={styles.container}>
      <View style={styles.grid}>
        {rows.map((row, rowIndex) => {
          // For incomplete rows, add empty placeholders to maintain spacing
          const filledRow = [...row];
          if (filledRow.length < ITEMS_PER_ROW) {
            const emptyCount = ITEMS_PER_ROW - filledRow.length;
            for (let i = 0; i < emptyCount; i++) {
              filledRow.push({ icon: "", label: "", href: "" } as MenuItem);
            }
          }

          return (
            <View key={`row-${rowIndex}`} style={styles.row}>
              {filledRow.map((item, colIndex) => {
                // Skip rendering empty placeholders
                if (!item.icon && !item.label && !item.href) {
                  return (
                    <View
                      key={`empty-${rowIndex}-${colIndex}`}
                      style={{ width: ITEM_WIDTH }}
                    />
                  );
                }

                return (
                  <AnimatedIconButton
                    key={`item-${rowIndex}-${colIndex}`}
                    icon={item.icon as any}
                    label={item.label}
                    href={item.href}
                    index={rowIndex * ITEMS_PER_ROW + colIndex}
                    iconSize={ICON_SIZE}
                    itemWidth={ITEM_WIDTH}
                  />
                );
              })}
            </View>
          );
        })}
      </View>
    </Card>
  );
}

/**
 * Animated version of IconButton with staggered entrance and press animations
 */
function AnimatedIconButton({
  icon,
  label,
  href,
  index,
  iconSize = 22,
  itemWidth,
}: {
  icon: any;
  label: string;
  href: string;
  index: number;
  iconSize?: number;
  itemWidth?: number;
}) {
  // Animation values - more subtle for smaller icons
  const iconScale = useSharedValue(0.5);
  const translateY = useSharedValue(30);
  const opacity = useSharedValue(0); // Add quick fade effect

  // Start animations when component mounts
  React.useEffect(() => {
    // Minimal staggered delay - just enough to create a wave effect
    const delay = index * 40; // Slightly faster staggering for 4 items per row

    // Quick fade in
    opacity.value = withDelay(
      delay,
      withTiming(1, { duration: 120, easing: Easing.in(Easing.ease) })
    );

    // Icon animation - slightly less bouncy for smaller icons
    iconScale.value = withDelay(
      delay,
      withSpring(1, { damping: 10, stiffness: 90, mass: 1 })
    );

    // Slide up animation with minimal delay
    translateY.value = withDelay(
      delay,
      withSpring(0, { damping: 14, stiffness: 110 })
    );
  }, []);

  // Function to handle press animation
  const handlePress = () => {
    // Create a more subtle pulse effect for smaller icons
    iconScale.value = withSequence(
      withTiming(0.9, { duration: 80, easing: Easing.inOut(Easing.quad) }),
      withSpring(1.05, { damping: 6, stiffness: 200 }),
      withSpring(1, { damping: 15 })
    );

    // Add a smaller bounce
    translateY.value = withSequence(
      withTiming(-3, { duration: 80 }),
      withSpring(0, { damping: 6, stiffness: 200 })
    );
  };

  // Icon animated styles
  const iconAnimatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: iconScale.value }, { translateY: translateY.value }],
  }));

  return (
    <Animated.View style={iconAnimatedStyle}>
      <IconButton
        icon={icon}
        label={label}
        href={href}
        onPress={handlePress}
        iconSize={iconSize}
        itemWidth={itemWidth}
      />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    paddingHorizontal: 16, // Increased padding for better spacing
    alignItems: "center", // Center the grid horizontally
  },
  grid: {
    flexDirection: "column",
    width: "100%",
    maxWidth: SCREEN_WIDTH, // Ensure grid doesn't exceed screen width
  },
  row: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "space-between", // Evenly distribute items
    paddingHorizontal: 0, // Remove horizontal padding as we're calculating it
  },
});
