import React from "react";
import { StyleSheet, View, Text } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  FadeIn,
} from "react-native-reanimated";
import { Colors } from "@/constants/Colors";
import { Card } from "@/components/ui/Card";

// Define attendance status types
export enum AttendanceStatus {
  ONTIME = "ontime",
  LATE = "late",
  ABSENT = "absent",
  FUTURE = "future",
}

// Define attendance data structure
export interface DailyAttendance {
  day: string;
  records: AttendanceStatus[];
}

// Define attendance summary data structure
export interface AttendanceSummary {
  ontime: number;
  late: number;
  absent: number;
}

interface AttendanceOverviewProps {
  data: {
    dailyAttendance: DailyAttendance[];
    summary: AttendanceSummary;
  };
}

/**
 * Attendance Overview Component
 * Displays a grid of attendance records and summary statistics
 */
export function AttendanceOverview({ data }: AttendanceOverviewProps) {
  // Animation values
  const cardScale = useSharedValue(0.95);
  const cardOpacity = useSharedValue(0);

  // Start animations when component mounts
  React.useEffect(() => {
    // Animate card
    cardScale.value = withTiming(1, {
      duration: 500,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
    cardOpacity.value = withTiming(1, { duration: 600 });
  }, []);

  // Card animated style
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cardOpacity.value,
    transform: [{ scale: cardScale.value }],
  }));

  // Get color for attendance status
  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case AttendanceStatus.ONTIME:
        return Colors.primary;
      case AttendanceStatus.LATE:
        // Lighter orange color
        return "#FFB74D";
      case AttendanceStatus.ABSENT:
        return Colors.error;
      case AttendanceStatus.FUTURE:
        return Colors.lightGray;
      default:
        return Colors.lightGray;
    }
  };

  // Calculate percentage for summary
  const calculatePercentage = (value: number) => {
    const total = data.summary.ontime + data.summary.late + data.summary.absent;
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  return (
    <View>
      {/* Attendance Card */}
      <Animated.View style={cardAnimatedStyle}>
        <Card style={styles.card}>
          <Text style={styles.title}>Attendance</Text>

          {/* Attendance Grid */}
          <View style={styles.gridContainer}>
            {data.dailyAttendance.map((day, dayIndex) => (
              <View key={dayIndex} style={styles.dayRow}>
                <View style={styles.dayRecords}>
                  {day.records.map((status, recordIndex) => (
                    <Animated.View
                      key={`${dayIndex}-${recordIndex}`}
                      entering={FadeIn.delay(
                        100 * (dayIndex + recordIndex)
                      ).duration(300)}
                      style={[
                        styles.statusBox,
                        { backgroundColor: getStatusColor(status) },
                      ]}
                    />
                  ))}
                </View>
              </View>
            ))}
          </View>

          {/* Summary Section */}
          <View style={styles.summaryContainer}>
            <View style={styles.summaryItem}>
              <View style={styles.summaryLabelContainer}>
                <View
                  style={[
                    styles.summaryIndicator,
                    { backgroundColor: Colors.primary },
                  ]}
                />
                <Text style={styles.summaryLabel}>On-time</Text>
              </View>
              <Text style={styles.summaryValue}>
                {calculatePercentage(data.summary.ontime)}%
              </Text>
            </View>

            <View style={styles.summaryItem}>
              <View style={styles.summaryLabelContainer}>
                <View
                  style={[
                    styles.summaryIndicator,
                    { backgroundColor: "#FFB74D" },
                  ]}
                />
                <Text style={styles.summaryLabel}>Late</Text>
              </View>
              <Text style={styles.summaryValue}>
                {calculatePercentage(data.summary.late)}%
              </Text>
            </View>

            <View style={styles.summaryItem}>
              <View style={styles.summaryLabelContainer}>
                <View
                  style={[
                    styles.summaryIndicator,
                    { backgroundColor: Colors.error },
                  ]}
                />
                <Text style={styles.summaryLabel}>Absent</Text>
              </View>
              <Text style={styles.summaryValue}>
                {calculatePercentage(data.summary.absent)}%
              </Text>
            </View>
          </View>
        </Card>
      </Animated.View>
    </View>
  );
}

// Mock data for the component
export const mockAttendanceData = {
  dailyAttendance: [
    {
      day: "",
      records: [
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ONTIME,
        AttendanceStatus.LATE,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ONTIME,
      ],
    },
    {
      day: "",
      records: [
        AttendanceStatus.ONTIME,
        AttendanceStatus.LATE,
        AttendanceStatus.ONTIME,
        AttendanceStatus.LATE,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ABSENT,
      ],
    },
    {
      day: "",
      records: [
        AttendanceStatus.LATE,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ONTIME,
        AttendanceStatus.ABSENT,
        AttendanceStatus.ONTIME,
        AttendanceStatus.FUTURE,
        AttendanceStatus.FUTURE,
        AttendanceStatus.FUTURE,
        AttendanceStatus.FUTURE,
        AttendanceStatus.FUTURE,
      ],
    },
  ],
  summary: {
    ontime: 15,
    late: 0,
    absent: 0,
  },
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 16,
    marginVertical: 12,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 16,
  },
  gridContainer: {
    marginBottom: 20,
  },
  dayRow: {
    marginBottom: 12,
  },
  dayRecords: {
    flex: 1,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 4,
  },
  statusBox: {
    width: 24,
    height: 24,
    borderRadius: 4,
  },
  summaryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  summaryItem: {
    flex: 1,
    alignItems: "center",
  },
  summaryLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  summaryIndicator: {
    width: 16,
    height: 4,
    borderRadius: 2,
    marginRight: 6,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.text,
  },
});
