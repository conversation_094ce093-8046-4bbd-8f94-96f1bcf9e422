import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleProp,
  ViewStyle,
} from "react-native";
import { useController, Control, FieldValues, Path } from "react-hook-form";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

interface SelectOption {
  label: string;
  value: string;
}

interface FormSelectProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  options: SelectOption[];
  label?: string;
  placeholder?: string;
  containerStyle?: StyleProp<ViewStyle>;
  rules?: object;
  multiple?: boolean; // New prop to enable multi-select
}

export function FormSelect<T extends FieldValues>({
  name,
  control,
  options,
  label,
  placeholder = "Select",
  containerStyle,
  rules,
  multiple = false, // Default to single select
}: FormSelectProps<T>) {
  const [modalVisible, setModalVisible] = useState(false);

  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
  });

  // Get selected options for display
  const getSelectedDisplay = () => {
    if (multiple) {
      const selectedValues = Array.isArray(field.value) ? field.value : [];
      if (selectedValues.length === 0) return placeholder;

      const selectedLabels = selectedValues
        .map(
          (value: string) =>
            options.find((option) => option.value === value)?.label
        )
        .filter(Boolean);

      return selectedLabels.join(", ");
    } else {
      const selectedOption = options.find(
        (option) => option.value === field.value
      );
      return selectedOption ? selectedOption.label : placeholder;
    }
  };

  // Handle option selection
  const handleSelect = (value: string) => {
    if (multiple) {
      const currentValues: string[] = Array.isArray(field.value)
        ? (field.value as string[])
        : [];
      let newValues;

      if (currentValues.includes(value)) {
        // Remove if already selected
        newValues = currentValues.filter((v: string) => v !== value);
      } else {
        // Add if not selected
        newValues = [...currentValues, value];
      }

      field.onChange(newValues);
    } else {
      field.onChange(value);
      setModalVisible(false);
    }
  };

  // Check if an option is selected
  const isOptionSelected = (value: string) => {
    if (multiple) {
      const selectedValues: string[] = Array.isArray(field.value)
        ? (field.value as string[])
        : [];
      return selectedValues.includes(value);
    } else {
      return field.value === value;
    }
  };

  // Get selected count for multi-select
  const getSelectedCount = () => {
    if (multiple && Array.isArray(field.value)) {
      return field.value.length;
    }
    return 0;
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
          {multiple && getSelectedCount() > 0 && (
            <Text style={styles.selectedCount}>
              {getSelectedCount()} selected
            </Text>
          )}
        </View>
      )}

      {/* Select Field */}
      <TouchableOpacity
        style={[styles.selectField, error && styles.selectFieldError]}
        onPress={() => setModalVisible(true)}
        activeOpacity={0.7}
      >
        <Text
          style={[
            styles.selectText,
            getSelectedDisplay() === placeholder && styles.placeholderText,
          ]}
          numberOfLines={multiple ? 2 : 1}
        >
          {getSelectedDisplay()}
        </Text>
        <Ionicons name="chevron-down" size={20} color={Colors.textLight} />
      </TouchableOpacity>

      {/* Error Message */}
      {error && <Text style={styles.errorText}>{error.message}</Text>}

      {/* Options Modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {label || "Select an option"}
                {multiple && ` (${getSelectedCount()} selected)`}
              </Text>
              <View style={styles.headerActions}>
                {multiple && (
                  <TouchableOpacity
                    onPress={() => setModalVisible(false)}
                    style={styles.doneButton}
                  >
                    <Text style={styles.doneButtonText}>Done</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  onPress={() => setModalVisible(false)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  style={styles.closeButton}
                >
                  <Ionicons name="close" size={24} color={Colors.text} />
                </TouchableOpacity>
              </View>
            </View>

            <FlatList
              data={options}
              keyExtractor={(item) => item.value}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.optionItem,
                    isOptionSelected(item.value) && styles.selectedOption,
                  ]}
                  onPress={() => handleSelect(item.value)}
                  activeOpacity={0.7}
                >
                  <Text
                    style={[
                      styles.optionText,
                      isOptionSelected(item.value) && styles.selectedOptionText,
                    ]}
                  >
                    {item.label}
                  </Text>
                  {isOptionSelected(item.value) && (
                    <Ionicons
                      name="checkmark"
                      size={20}
                      color={Colors.primary}
                    />
                  )}
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.optionsList}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  labelContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
  },
  selectedCount: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: "500",
  },
  selectField: {
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    padding: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    minHeight: 48,
  },
  selectFieldError: {
    borderColor: Colors.error || "red",
  },
  selectText: {
    fontSize: 16,
    color: Colors.text,
    flex: 1,
    marginRight: 8,
  },
  placeholderText: {
    color: Colors.textLight,
  },
  errorText: {
    fontSize: 12,
    color: Colors.error || "red",
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: Colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: "70%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.text,
    flex: 1,
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  doneButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  doneButtonText: {
    color: Colors.white,
    fontWeight: "500",
    fontSize: 14,
  },
  closeButton: {
    padding: 4,
  },
  optionsList: {
    paddingBottom: 20,
  },
  optionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  selectedOption: {
    backgroundColor: `${Colors.primary}10`,
  },
  optionText: {
    fontSize: 16,
    color: Colors.text,
  },
  selectedOptionText: {
    color: Colors.primary,
    fontWeight: "500",
  },
});
